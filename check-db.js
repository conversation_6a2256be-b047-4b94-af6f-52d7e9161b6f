// Simple script to check database directly
require('dotenv').config()

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables')
    console.log('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl)
    console.log('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey)
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkDatabase() {
    console.log('🔍 Checking database connection and demo users...')
    console.log('='.repeat(50))
    
    try {
        // Test connection
        const { data: testData, error: testError } = await supabase
            .from('users')
            .select('count')
            .limit(1)
        
        if (testError) {
            console.error('❌ Database connection failed:', testError.message)
            return
        }
        
        console.log('✅ Database connection successful')
        
        // Check for demo users
        const demoNips = ['199001010001', '199002020002', '199001001']
        
        for (const nip of demoNips) {
            console.log(`\n🔍 Checking user with NIP: ${nip}`)
            
            const { data: user, error } = await supabase
                .from('users')
                .select('id, nip, name, email, status, password_hash')
                .eq('nip', nip)
                .single()
            
            if (error) {
                console.log(`❌ Error or user not found: ${error.message}`)
                continue
            }
            
            if (user) {
                console.log('✅ User found:', {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    nip: user.nip,
                    status: user.status,
                    hasPasswordHash: !!user.password_hash,
                    passwordHashLength: user.password_hash?.length || 0
                })
            }
        }
        
        // Check total users
        const { data: allUsers, error: countError } = await supabase
            .from('users')
            .select('id, nip, name, email, status, password_hash')
        
        if (!countError && allUsers) {
            console.log(`\n📊 Total users in database: ${allUsers.length}`)
            allUsers.forEach(user => {
                console.log(`  - ${user.name} (${user.nip}) - ${user.status} - Password: ${!!user.password_hash}`)
            })
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message)
    }
}

checkDatabase()
    .then(() => {
        console.log('\n🎉 Database check completed!')
        process.exit(0)
    })
    .catch((error) => {
        console.error('❌ Check failed:', error)
        process.exit(1)
    })
