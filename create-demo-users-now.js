// Create demo users immediately
require('dotenv').config()

const { createClient } = require('@supabase/supabase-js')
const bcrypt = require('bcryptjs')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('🚀 Creating demo users...')
console.log('Environment check:')
console.log('- URL:', supabaseUrl ? '✅' : '❌')
console.log('- Service Key:', supabaseServiceKey ? '✅' : '❌')

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing environment variables')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const demoUsers = [
    {
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'password123',
        nip: '199001010001',
        phone: '081234567890'
    },
    {
        name: 'HR Admin',
        email: '<EMAIL>',
        password: 'password123',
        nip: '199002020002',
        phone: '081234567891'
    },
    {
        name: 'Pegawai Demo',
        email: '<EMAIL>',
        password: 'password123',
        nip: '199001001',
        phone: '081234567892'
    }
]

async function createDemoUsers() {
    try {
        // First, check current users
        console.log('\n🔍 Checking existing users...')
        const { data: existingUsers, error: checkError } = await supabase
            .from('users')
            .select('id, nip, name, email, status, password_hash')
        
        if (checkError) {
            console.error('❌ Error checking users:', checkError.message)
            return
        }
        
        console.log(`Found ${existingUsers?.length || 0} existing users`)
        if (existingUsers && existingUsers.length > 0) {
            existingUsers.forEach(user => {
                console.log(`  - ${user.name} (${user.nip}) - Password: ${!!user.password_hash}`)
            })
        }
        
        // Create or update demo users
        for (const userData of demoUsers) {
            console.log(`\n👤 Processing: ${userData.name} (${userData.nip})`)
            
            // Check if user exists
            const { data: existing } = await supabase
                .from('users')
                .select('id, password_hash')
                .eq('nip', userData.nip)
                .single()
            
            if (existing) {
                console.log('  ✅ User exists')
                
                // Update password if missing
                if (!existing.password_hash) {
                    console.log('  🔑 Adding password hash...')
                    const passwordHash = await bcrypt.hash(userData.password, 12)
                    
                    const { error: updateError } = await supabase
                        .from('users')
                        .update({ password_hash: passwordHash })
                        .eq('id', existing.id)
                    
                    if (updateError) {
                        console.error('  ❌ Error updating password:', updateError.message)
                    } else {
                        console.log('  ✅ Password hash added')
                    }
                } else {
                    console.log('  ✅ Password hash already exists')
                }
            } else {
                console.log('  ➕ Creating new user...')
                
                const passwordHash = await bcrypt.hash(userData.password, 12)
                
                const { data: newUser, error: createError } = await supabase
                    .from('users')
                    .insert({
                        nip: userData.nip,
                        name: userData.name,
                        email: userData.email,
                        password_hash: passwordHash,
                        phone: userData.phone,
                        status: 'active'
                    })
                    .select('id, name, nip')
                    .single()
                
                if (createError) {
                    console.error('  ❌ Error creating user:', createError.message)
                } else {
                    console.log('  ✅ User created:', newUser)
                }
            }
        }
        
        // Final verification
        console.log('\n🔍 Final verification...')
        for (const userData of demoUsers) {
            const { data: user, error } = await supabase
                .from('users')
                .select('id, nip, name, email, status, password_hash')
                .eq('nip', userData.nip)
                .single()
            
            if (error) {
                console.log(`❌ ${userData.nip}: Not found - ${error.message}`)
            } else {
                console.log(`✅ ${userData.nip}: ${user.name} - Password: ${!!user.password_hash}`)
            }
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message)
    }
}

createDemoUsers()
    .then(() => {
        console.log('\n🎉 Demo users setup completed!')
        console.log('\n📋 Login credentials:')
        console.log('NIP: 199001010001, Password: password123')
        console.log('NIP: 199002020002, Password: password123')
        console.log('NIP: 199001001, Password: password123')
        process.exit(0)
    })
    .catch((error) => {
        console.error('❌ Setup failed:', error)
        process.exit(1)
    })
