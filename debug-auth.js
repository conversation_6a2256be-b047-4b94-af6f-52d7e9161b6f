// Simple debug script to check authentication
const { UserRepository } = require('./src/infrastructure/database/repositories/UserRepository')
const { AuthenticateUser } = require('./src/use-cases/user/AuthenticateUser')

async function debugAuth() {
    console.log('🔍 Debug Authentication System')
    console.log('='.repeat(40))
    
    const userRepository = new UserRepository()
    const authenticateUser = new AuthenticateUser(userRepository)
    
    const testCredentials = [
        { identifier: '199001010001', password: 'password123' },
        { identifier: '199002020002', password: 'password123' },
        { identifier: '199001001', password: 'password123' },
        { identifier: '<EMAIL>', password: 'password123' }
    ]
    
    for (const cred of testCredentials) {
        console.log(`\n🧪 Testing: ${cred.identifier}`)
        console.log('-'.repeat(30))
        
        try {
            // Test findUserByIdentifier
            const isEmail = cred.identifier.includes('@')
            const user = isEmail 
                ? await userRepository.findByEmail(cred.identifier)
                : await userRepository.findByNip(cred.identifier)
            
            if (user) {
                console.log('✅ User found via repository:', {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    nip: user.nip,
                    status: user.status
                })
            } else {
                console.log('❌ User not found via repository')
                continue
            }
            
            // Test findForAuthentication
            const authData = await userRepository.findForAuthentication(cred.identifier)
            if (authData) {
                console.log('✅ Auth data found:', {
                    userId: authData.user.id,
                    hasPasswordHash: !!authData.passwordHash,
                    passwordHashLength: authData.passwordHash?.length || 0
                })
            } else {
                console.log('❌ Auth data not found')
                continue
            }
            
            // Test full authentication
            const result = await authenticateUser.execute({
                identifier: cred.identifier,
                password: cred.password,
                ipAddress: 'debug',
                userAgent: 'debug-script'
            })
            
            if (result.success) {
                console.log('✅ Authentication successful!')
            } else {
                console.log('❌ Authentication failed:', result.error?.message)
            }
            
        } catch (error) {
            console.error('❌ Error during test:', error.message)
        }
    }
}

// Run debug
debugAuth()
    .then(() => {
        console.log('\n🎉 Debug completed!')
        process.exit(0)
    })
    .catch((error) => {
        console.error('❌ Debug failed:', error)
        process.exit(1)
    })
