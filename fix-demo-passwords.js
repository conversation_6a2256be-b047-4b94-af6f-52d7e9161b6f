// Fix demo user passwords
require('dotenv').config()

const { createClient } = require('@supabase/supabase-js')
const bcrypt = require('bcryptjs')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('🔧 Fixing demo user passwords...')
console.log('Environment check:')
console.log('- URL:', supabaseUrl ? '✅' : '❌')
console.log('- Service Key:', supabaseServiceKey ? '✅' : '❌')

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing environment variables')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const demoUsers = [
    {
        nip: '199001010001',
        password: 'password123'
    },
    {
        nip: '199002020002',
        password: 'password123'
    },
    {
        nip: '199001001',
        password: 'password123'
    }
]

async function fixDemoPasswords() {
    try {
        console.log('\n🔍 Checking and fixing demo user passwords...')
        
        for (const userData of demoUsers) {
            console.log(`\n👤 Processing user with NIP: ${userData.nip}`)
            
            // Check if user exists
            const { data: existingUser, error: checkError } = await supabase
                .from('users')
                .select('id, nip, name, email, password_hash')
                .eq('nip', userData.nip)
                .single()
            
            if (checkError) {
                console.log(`  ❌ User not found: ${checkError.message}`)
                continue
            }
            
            console.log(`  ✅ User found: ${existingUser.name}`)
            console.log(`  📊 Current password hash length: ${existingUser.password_hash?.length || 0}`)
            
            // Generate new password hash
            console.log(`  🔐 Generating new password hash...`)
            const newPasswordHash = await bcrypt.hash(userData.password, 12)
            console.log(`  📊 New password hash length: ${newPasswordHash.length}`)
            
            // Test the new hash
            const testVerification = await bcrypt.compare(userData.password, newPasswordHash)
            console.log(`  🧪 Hash verification test: ${testVerification ? '✅ PASS' : '❌ FAIL'}`)
            
            if (!testVerification) {
                console.log(`  ❌ Hash generation failed for ${userData.nip}`)
                continue
            }
            
            // Update password hash in database
            const { error: updateError } = await supabase
                .from('users')
                .update({ 
                    password_hash: newPasswordHash,
                    updated_at: new Date().toISOString()
                })
                .eq('nip', userData.nip)
            
            if (updateError) {
                console.log(`  ❌ Failed to update password: ${updateError.message}`)
            } else {
                console.log(`  ✅ Password updated successfully`)
                
                // Verify the update
                const { data: verifyUser } = await supabase
                    .from('users')
                    .select('password_hash')
                    .eq('nip', userData.nip)
                    .single()
                
                if (verifyUser) {
                    const finalVerification = await bcrypt.compare(userData.password, verifyUser.password_hash)
                    console.log(`  🔍 Final verification: ${finalVerification ? '✅ SUCCESS' : '❌ FAILED'}`)
                }
            }
        }
        
        console.log('\n🎉 Password fix process completed!')
        console.log('\n📋 Test these credentials:')
        demoUsers.forEach(user => {
            console.log(`NIP: ${user.nip}, Password: ${user.password}`)
        })
        
    } catch (error) {
        console.error('❌ Error:', error.message)
    }
}

fixDemoPasswords()
    .then(() => {
        console.log('\n✅ Process completed successfully!')
        process.exit(0)
    })
    .catch((error) => {
        console.error('❌ Process failed:', error)
        process.exit(1)
    })
