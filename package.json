{"name": "untitled", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-auth": "npx tsx scripts/setup-auth.ts", "create-test-users": "node scripts/create-users-simple.mjs", "create-test-users-ts": "npx tsx scripts/create-test-user.ts", "db:push": "npx prisma db push", "db:generate": "npx prisma generate", "check-db": "node check-db.js", "create-demo-users": "node create-demo-users-now.js", "test-db": "node test-db-connection.js"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@next-auth/supabase-adapter": "^0.2.1", "@prisma/client": "^6.8.2", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.49.8", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "next": "15.1.8", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "prisma": "^6.8.2", "supabase": "^2.23.4", "tailwindcss": "^3.4.1", "typescript": "^5"}}