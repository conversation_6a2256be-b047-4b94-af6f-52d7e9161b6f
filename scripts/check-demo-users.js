const { createClient } = require('@supabase/supabase-js')
const bcrypt = require('bcryptjs')

// Load environment variables
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Demo test users data
const testUsers = [
    {
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'password123',
        nip: '************',
        phone: '081234567890'
    },
    {
        name: 'HR Admin',
        email: '<EMAIL>',
        password: 'password123',
        nip: '************',
        phone: '081234567891'
    },
    {
        name: '<PERSON><PERSON><PERSON><PERSON> Demo',
        email: '<EMAIL>',
        password: 'password123',
        nip: '*********',
        phone: '081234567892'
    }
]

async function checkAndCreateDemoUsers() {
    console.log('🔍 Checking demo users...')
    console.log('='.repeat(40))
    
    for (const userData of testUsers) {
        try {
            console.log(`Checking user: ${userData.name} (NIP: ${userData.nip})...`)
            
            // Check if user already exists
            const { data: existingUser, error: checkError } = await supabase
                .from('users')
                .select('id, nip, name, email, password_hash')
                .eq('nip', userData.nip)
                .single()
            
            if (existingUser) {
                console.log(`  ✅ User exists:`, {
                    id: existingUser.id,
                    name: existingUser.name,
                    email: existingUser.email,
                    nip: existingUser.nip,
                    hasPassword: !!existingUser.password_hash
                })
                
                // Check if password hash exists
                if (!existingUser.password_hash) {
                    console.log(`  ⚠️  User ${userData.nip} missing password hash, updating...`)
                    const passwordHash = await bcrypt.hash(userData.password, 12)
                    
                    const { error: updateError } = await supabase
                        .from('users')
                        .update({ password_hash: passwordHash })
                        .eq('id', existingUser.id)
                    
                    if (updateError) {
                        console.error(`  ❌ Error updating password for ${userData.nip}:`, updateError.message)
                    } else {
                        console.log(`  ✅ Password hash updated for ${userData.nip}`)
                    }
                }
                continue
            }
            
            console.log(`  ➕ Creating user: ${userData.name}...`)
            
            // Hash password
            const passwordHash = await bcrypt.hash(userData.password, 12)
            
            // Create user
            const { data: user, error } = await supabase
                .from('users')
                .insert({
                    nip: userData.nip,
                    name: userData.name,
                    email: userData.email,
                    password_hash: passwordHash,
                    phone: userData.phone,
                    status: 'active'
                })
                .select('id, name, email, nip')
                .single()
            
            if (error) {
                throw new Error(error.message)
            }
            
            console.log(`  ✅ User created successfully:`, {
                id: user.id,
                name: user.name,
                email: user.email,
                nip: user.nip
            })
        } catch (error) {
            console.error(`  ❌ Error processing user ${userData.name}:`, error.message)
        }
    }
    
    console.log('\n📋 Demo Accounts Summary:')
    console.log('='.repeat(25))
    console.log('Super Admin:')
    console.log('  NIP: ************')
    console.log('  Password: password123')
    console.log('')
    console.log('HR Admin:')
    console.log('  NIP: ************')
    console.log('  Password: password123')
    console.log('')
    console.log('Pegawai:')
    console.log('  NIP: *********')
    console.log('  Password: password123')
    console.log('')
    console.log('💡 Note: Try logging in with any of these accounts.')
}

// Run the function
checkAndCreateDemoUsers()
    .then(() => {
        console.log('\n🎉 Demo users check completed!')
        process.exit(0)
    })
    .catch((error) => {
        console.error('❌ Error:', error)
        process.exit(1)
    })
