// Test database connection and check users table
require('dotenv').config()

const { createClient } = require('@supabase/supabase-js')

async function testConnection() {
    console.log('🔍 Testing Supabase connection...')
    console.log('='.repeat(40))
    
    // Check environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    console.log('Environment variables:')
    console.log('- NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing')
    console.log('- SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅ Set' : '❌ Missing')
    
    if (!supabaseUrl || !supabaseServiceKey) {
        console.error('❌ Missing required environment variables')
        return
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    try {
        // Test basic connection
        console.log('\n🔗 Testing basic connection...')
        const { data: healthCheck, error: healthError } = await supabase
            .from('users')
            .select('count')
            .limit(1)
        
        if (healthError) {
            console.error('❌ Connection failed:', healthError.message)
            return
        }
        
        console.log('✅ Connection successful')
        
        // Check users table structure
        console.log('\n📋 Checking users table...')
        const { data: users, error: usersError } = await supabase
            .from('users')
            .select('*')
            .limit(5)
        
        if (usersError) {
            console.error('❌ Error querying users table:', usersError.message)
            return
        }
        
        console.log(`✅ Users table accessible, found ${users?.length || 0} users`)
        
        if (users && users.length > 0) {
            console.log('\n👥 Sample users:')
            users.forEach((user, index) => {
                console.log(`${index + 1}. ${user.name} (NIP: ${user.nip}, Email: ${user.email})`)
                console.log(`   Status: ${user.status}, Has Password: ${!!user.password_hash}`)
            })
        }
        
        // Specifically check for demo users
        console.log('\n🎯 Checking for demo users...')
        const demoNips = ['199001010001', '199002020002', '199001001']
        
        for (const nip of demoNips) {
            const { data: user, error } = await supabase
                .from('users')
                .select('id, nip, name, email, status, password_hash')
                .eq('nip', nip)
                .single()
            
            if (error) {
                console.log(`❌ NIP ${nip}: ${error.message}`)
            } else if (user) {
                console.log(`✅ NIP ${nip}: Found - ${user.name} (${user.email})`)
                console.log(`   Status: ${user.status}, Password Hash: ${user.password_hash ? 'Yes' : 'No'}`)
            }
        }
        
        // Test the exact query that's failing
        console.log('\n🔍 Testing exact query that\'s failing...')
        const { data: testUser, error: testError } = await supabase
            .from('users')
            .select(`
                *,
                role:roles(*),
                department:departments(*)
            `)
            .eq('nip', '199001010001')
            .single()
        
        if (testError) {
            console.log('❌ Test query failed:', testError.message)
            console.log('Error details:', testError)
        } else if (testUser) {
            console.log('✅ Test query successful:', {
                id: testUser.id,
                name: testUser.name,
                nip: testUser.nip,
                email: testUser.email,
                status: testUser.status,
                hasPasswordHash: !!testUser.password_hash
            })
        }
        
    } catch (error) {
        console.error('❌ Unexpected error:', error)
    }
}

testConnection()
    .then(() => {
        console.log('\n🎉 Test completed!')
        process.exit(0)
    })
    .catch((error) => {
        console.error('❌ Test failed:', error)
        process.exit(1)
    })
